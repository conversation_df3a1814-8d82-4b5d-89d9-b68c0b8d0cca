@use 'colors' as *;

.partners {
    margin-top: 2rem;
    padding: 3rem 0;
    background-color: $bg-tertiary;
    border-top: 1px solid $border-medium;

}

.partners-header {
    text-align: center;
}

.partners-list {
    display: flex;
    justify-content: center;
    align-items: center;;
    flex-wrap: wrap;
    gap: 3rem;
    margin-top: 2rem;

    @media screen and (max-width: 768px) {
        width: 100%;
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }

    a {
        display: block;
        text-decoration: none;
        color: inherit;
        cursor: pointer;
        transition: transform 0.3s ease;

        &:hover {
            transform: scale(1.05);
        }
    }

    .partner-name {
        font-weight: bold;
        font-size: 1.2rem;
    }

    img {
        max-width: 150px;
        max-height: 60px;
        filter: grayscale(100%);
        width: 100%;
        height: auto;
        object-fit: contain;
    }
}

footer {
    background-color: $brand-green-primary;
    padding: 4rem 1rem;
    color: $text-inverse;

    .container {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 5rem;
        grid-template-areas:
            "info contact finance"
            "copyright copyright copyright";
        @media (max-width: 768px) {
        grid-template-columns: 1fr;
        grid-template-areas:
            "info"
            "contact"
            "finance"
            "copyright";
    }
    }

    .footer-info {
        h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            color: $text-inverse;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;

            .contact-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
                color: $brand-green-light;
                flex-shrink: 0;
                margin-top: 0.125rem; // Slight offset to align with text

                svg {
                    width: 16px;
                    height: 16px;
                }
            }

            .info {

                p {
                    margin: 0 0 0.5rem 0;
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: $text-inverse;
                }

                address {
                    font-style: normal;

                    p {
                        margin: 0;
                        font-size: 0.875rem;
                        line-height: 1.5;
                        color: rgba($text-inverse, 0.8);
                        font-weight: 400;
                    }
                }

                a {
                    color: rgba($text-inverse, 0.9);
                    text-decoration: none;
                    font-size: 0.875rem;
                    line-height: 1.5;
                    font-weight: 400;
                    transition: color 0.3s ease;

                    &:hover {
                        color: $brand-green-light;
                        text-decoration: underline;
                    }
                }
            }
        }

    }

    .club-info {
        grid-area: info;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;

        .footer-logo {
            max-width: 120px;
            height: auto;
            filter: brightness(0) invert(1); // Invert logo to white
        }

        h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            color: $text-inverse;
        }

        > p {
            font-size: 0.9rem;
            line-height: 1.6;
            color: rgba($text-inverse, 0.8);
            margin: 0;
        }

        .social-links {
            margin-top: 1rem;

            h4 {
                font-size: 1rem;
                font-weight: 600;
                margin: 0 0 1rem 0;
                color: $text-inverse;
            }

            .social-list {
                display: flex;
                flex-direction: column;
                gap: 0.75rem;

                a {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    color: $text-inverse;
                    text-decoration: none;
                    font-size: 0.875rem;
                    font-weight: 500;
                    transition: all 0.3s ease;

                    &:hover {
                        color: $brand-green-light;

                        .social-icon {
                            background-color: $brand-green-light;
                            color: $brand-green-primary;
                            transform: scale(1.05);
                        }
                    }

                    .social-icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 36px;
                        height: 36px;
                        background-color: rgba($text-inverse, 0.1);
                        border-radius: 10px; // Squircle effect
                        color: $text-inverse;
                        transition: all 0.3s ease;
                        flex-shrink: 0;

                        svg {
                            width: 18px;
                            height: 18px;
                        }
                    }

                    span {
                        font-size: 0.875rem;
                    }
                }
            }
        }
    }

    .contact {
        grid-area: contact;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;

    }

    .finance {
        grid-area: finance;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .copyright {
        grid-area: copyright;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid rgba($text-inverse, 0.2);

        p {
            font-size: 0.875rem;
            line-height: 1.5;
            color: rgba($text-inverse, 0.7);
            margin: 0;
        }

        div {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 1rem;

            @media screen and (max-width: 768px) {
                flex-direction: column;
                gap: 0.5rem;
            }

            a {
                color: rgba($text-inverse, 0.8);
                text-decoration: none;
                font-size: 0.875rem;
                line-height: 1.5;
                font-weight: 400;
                transition: color 0.3s ease;

                &:hover {
                    color: $brand-green-light;
                    text-decoration: underline;
                }
            }

            span {
                color: rgba($text-inverse, 0.5);
                font-size: 0.875rem;
            }
        }
    }

}
